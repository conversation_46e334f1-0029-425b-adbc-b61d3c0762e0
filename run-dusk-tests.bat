@echo off
echo Running Laravel Dusk Tests
echo =========================
echo.

REM Check if ChromeDriver is running
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:9515/status' -UseBasicParsing; Write-Host 'ChromeDriver is running.' } catch { Write-Host 'ChromeDriver is not running. Starting ChromeDriver...'; Start-Process 'chromedriver' -ArgumentList '--port=9515' -WindowStyle Normal }"

echo.
echo Starting Laravel development server...
start /B php artisan serve

echo.
echo Waiting for server to start...
timeout /t 5 /nobreak > nul

echo.
echo Running Dusk tests...
php artisan dusk %*

echo.
echo Tests completed.
echo.

REM Ask if user wants to stop the server
set /p choice=Do you want to stop the Laravel development server? (Y/N): 
if /i "%choice%"=="Y" (
    echo Stopping Laravel development server...
    taskkill /f /im php.exe > nul 2>&1
    echo Server stopped.
) else (
    echo Server is still running. You can stop it manually later.
)

echo.
echo Done!
