#!/bin/bash
echo "Running Laravel Dusk Tests"
echo "========================="
echo ""

# Check if ChromeDriver is running
if curl -s http://localhost:9515/status > /dev/null; then
    echo "ChromeDriver is running."
else
    echo "ChromeDriver is not running. Starting ChromeDriver..."
    chromedriver --port=9515 &
    sleep 2
fi

echo ""
echo "Starting Laravel development server..."
php artisan serve > /dev/null 2>&1 &
SERVER_PID=$!

echo ""
echo "Waiting for server to start..."
sleep 5

echo ""
echo "Running Dusk tests..."
php artisan dusk "$@"

echo ""
echo "Tests completed."
echo ""

# Ask if user wants to stop the server
read -p "Do you want to stop the Laravel development server? (Y/n): " choice
choice=${choice:-Y}
if [[ $choice =~ ^[Yy]$ ]]; then
    echo "Stopping Laravel development server..."
    kill $SERVER_PID
    echo "Server stopped."
else
    echo "Server is still running. You can stop it manually later."
fi

echo ""
echo "Done!"
