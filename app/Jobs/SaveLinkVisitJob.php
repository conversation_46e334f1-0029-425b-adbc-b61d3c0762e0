<?php

namespace App\Jobs;

use App\Models\LinkVisit;
use donatj\UserAgent\UserAgentParser;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use MaxMind\Db\Reader;

class SaveLinkVisitJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;


    public function __construct(private readonly int $linkId, private readonly array $request)
    {
    }

    public function handle(): void
    {
        DB::transaction(function () {
            $userAgentParser = new UserAgentParser();
            $parsedUserAgent = $userAgentParser->parse($this->request['headers']->get('User-Agent'));
            $maxMindReader = new Reader(storage_path('maxmind/GeoLite2-Country.mmdb'));

            LinkVisit::create([
                'link_id' => $this->linkId,
                'ip' => $this->request['ip'],
                'browser' => $parsedUserAgent->browser(),
                'country' => $maxMindReader->get($this->request['ip'])['country']['iso_code'] ?? null,
                'platform' => $parsedUserAgent->platform(),
                'domain_id' => $this->request['domain_id'],
            ]);
        });
    }
}
