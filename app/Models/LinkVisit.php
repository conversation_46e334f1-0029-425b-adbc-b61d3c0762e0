<?php

namespace App\Models;

use App\Observers\LinkVisitObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

#[ObservedBy([LinkVisitObserver::class])]
class LinkVisit extends Model
{
    use HasFactory, HasTimestamps;

    public const UPDATED_AT = null;
    protected $guarded = [];

    public function link(): BelongsTo
    {
        return $this->belongsTo(Link::class);
    }
}
