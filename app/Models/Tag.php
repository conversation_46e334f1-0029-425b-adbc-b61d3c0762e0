<?php

namespace App\Models;

use App\History\MyLogsActivity;
use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\Activitylog\Traits\LogsActivity;

class Tag extends Model
{
    protected $guarded = [];

    use HasFactory, HasTimestamps, LogsActivity, MyLogsActivity;

    public function __toString()
    {
        return $this->name;
    }

    public function links(): BelongsToMany
    {
        return $this->belongsToMany(Link::class, LinkTag::class);
    }
}
