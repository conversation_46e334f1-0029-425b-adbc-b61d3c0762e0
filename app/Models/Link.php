<?php

namespace App\Models;

use App\History\MyLogsActivity;
use App\Observers\LinkObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([LinkObserver::class])]
class Link extends Model
{
    use HasFactory, HasTimestamps, LogsActivity, MyLogsActivity;

    protected $guarded = [
        'short_path'
    ];

    public function scopeAvailable(Builder $query): Builder
    {
        return $query
            ->where('is_active', true)
            ->where(function ($query) {
                $query
                    ->whereNull('available_at')
                    ->orWhere('available_at', '<=', now());
            })
            ->where(function ($query) {
                $query
                    ->whereNull('unavailable_at')
                    ->orWhere('unavailable_at', '>=', now());
            });
    }

    public function scopeHasPassword(Builder $query): Builder
    {
        return $query->whereNotNull('password');
    }

    public function scopeForCurrentDomain(Builder $query): Builder
    {
        $currentDomain = current_domain();

        if (!isset($currentDomain)) {
            return $query->whereRaw('1 = 0'); // Forces an empty result set
        }

        return $query->whereHas('domains', function (Builder $query) use ($currentDomain) {
            $query->where('domains.id', $currentDomain->id);
        });
    }

    public function isAvailable(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->is_active && ($this->available_at === null || $this->available_at <= now()) && ($this->unavailable_at === null || $this->unavailable_at >= now()),
        );
    }

    public function hasPassword(): Attribute
    {
        return Attribute::make(
            get: fn() => isset($this->password),
        );
    }

    public function visits(): HasMany
    {
        return $this->hasMany(LinkVisit::class, 'link_id');
    }


    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, LinkTag::class);
    }

    public function domains(): BelongsToMany
    {
        return $this->belongsToMany(Domain::class, LinkDomain::class);
    }

    public function __toString()
    {
        return $this->short_path;
    }

    protected function casts(): array
    {
        return [
            'available_at' => 'datetime',
            'unavailable_at' => 'datetime',
            'forward_query_parameters' => 'boolean',
            'is_active' => 'boolean',
        ];
    }
}
