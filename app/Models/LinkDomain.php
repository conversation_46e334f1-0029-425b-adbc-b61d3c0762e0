<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LinkDomain extends Model
{
    protected $table = 'link_domain';

    public $timestamps = false;

    public function link(): BelongsTo
    {
        return $this->belongsTo(Link::class);
    }

    public function domain(): BelongsTo
    {
        return $this->belongsTo(Domain::class);
    }
}
