<?php

namespace App\Models;

use App\Enums\Protocol;
use App\History\MyLogsActivity;
use App\Observers\DomainObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;
use Spatie\Activitylog\Traits\LogsActivity;

#[ObservedBy([DomainObserver::class])]
class Domain extends Model
{
    use HasFactory, HasTimestamps, LogsActivity, MyLogsActivity;

    public const UPDATED_AT = null;

    protected $guarded = [];

    public function links(): BelongsToMany
    {
        return $this->belongsToMany(Link::class, LinkDomain::class);
    }

    public function hostWithoutPort(): Attribute
    {
        return Attribute::make(
            get: function ($value, array $attributes) {
                if (empty($this->host)) {
                    return '';
                }

                // Return the full host if it doesn't contain a colon
                if (!Str::contains($this->host, ':')) {
                    return $this->host;
                }

                // Return the part before the colon
                return Str::before($this->host, ':');
            },
        );
    }

    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    public function __toString()
    {
        return $this->host;
    }

    protected function casts(): array
    {
        return [
            'protocol' => Protocol::class,
        ];
    }
}
