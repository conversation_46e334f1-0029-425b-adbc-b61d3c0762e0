<?php

namespace App\Services;

use App\Jobs\SaveLinkVisitJob;
use App\Models\Link;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;

class LinkVisitService
{
    public static function redirectToOriginalUrl(Link $link): Redirector|RedirectResponse
    {
        SaveLinkVisitJob::dispatch($link->id, [
            'headers' => request()->headers,
            'ip' => request()->ip(),
            'domain_id' => current_domain()->id,
        ]);

        $queryParameters = collect();
        if ($link->send_ref_query_parameter)
            $queryParameters->put('ref', request()->httpHost());
        if ($link->forward_query_parameters)
            $queryParameters = $queryParameters->merge(request()->query);

        $url = Request::create($link->original_url);
        if (!$queryParameters->isEmpty())
            $url = $url->fullUrlWithQuery($queryParameters->toArray());
        else
            $url = $url->fullUrl();

        return redirect($url);
    }
}
