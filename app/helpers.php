<?php

use App\Models\Domain;
use App\Models\Link;

if (!function_exists('current_domain')) {
    function current_domain(): Domain|null
    {
        return Domain::where('host', request()->httpHost())
            ->where('protocol', request()->getScheme())
            ->first();
    }
}

if (!function_exists('get_short_url')) {
    function get_short_url(Link $link, Domain|null $domain = null): string
    {
        if (!(isset($domain) && $link->domains()->where('domains.id', $domain->id)->exists())) {
            // If the domain is not provided or the link is not associated with the provided domain,
            $currentDomain = current_domain();
            if ($currentDomain && $link->domains()->where('domains.id', $currentDomain->id)->exists()) {
                // If the link is associated with the current domain, use the current domain
                $domain = $currentDomain;
            } else {
                // Otherwise, use the first domain associated with the link
                $domain = $link->domains()->first();
            }
        }

        URL::useOrigin("{$domain->protocol->value}://{$domain->host}");

        return URL::route('link.redirect', ['short_path' => $link->short_path]);
    }
}
