<?php

namespace App\Observers;

use App\Models\Domain;
use Illuminate\Validation\ValidationException;
use <PERSON><PERSON>\Octane\Octane;

class DomainObserver
{
	/**
	 * @throws ValidationException
	 */
	public function saving(Domain $domain): void
	{
		if (!($domain->is_active && $domain->is_admin_panel_available) && !Domain::active()
				->where('is_admin_panel_available', true)
				->where('id', '!=', $domain->id)
				->exists()) {
			throw ValidationException::withMessages([
				'is_admin_panel_available' => 'At least one active domain must have admin panel available.',
			]);
		}

		if ($domain->isDirty(['is_active', 'is_admin_panel_available'])) {
			filament()->getCurrentPanel()?->boot();
		}
	}
}
