<?php

namespace App\Observers;

use App\Models\Link;
use Illuminate\Support\Str;

class LinkObserver
{
    public function creating(Link $link): void
    {
        if (isset($link->slug)) {
            $link->short_path = $this->generateUniqueSlug($link, $link->slug);
            return;
        }

        // Generate a default unique slug if no slug is provided
        $link->short_path = $this->generateUniqueSlug($link);
    }

    private function generateUniqueSlug(Link $link, ?string $baseSlug = null): string
    {
        if ($baseSlug) {
            // First try the original slug
            if (!Link::where('short_path', $baseSlug)->exists()) {
                return $baseSlug;
            }

            // If taken, try with postfix
            do {
                $slug = $baseSlug.Str::random(6);
                $exists = Link::where('short_path', $slug)->exists();
            } while ($exists);

            return $slug;
        }

        // Generate random slug if no base slug provided
        do {
            $slug = Str::random(6);
            $exists = Link::where('short_path', $slug)->exists();
        } while ($exists);

        return $slug;
    }
}
