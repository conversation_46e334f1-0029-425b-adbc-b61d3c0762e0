<?php

namespace App\Filament\Resources\DomainResource\Pages;

use App\Filament\Resources\DomainResource;
use App\Models\Domain;
use Filament\Actions\DeleteAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditDomain extends EditRecord
{
    protected static string $resource = DomainResource::class;

    protected function getHeaderActions(): array
    {
        return [
            DeleteAction::make()
                ->before(function (Domain $record) {
                    if ($record->links()->exists()) {
                        Notification::make()
                            ->danger()
                            ->title('Cannot delete domain')
                            ->body('This domain cannot be deleted because it has links attached to it. Please remove all links from this domain first.')
                            ->send();

                        return false;
                    }
                }),
        ];
    }

    protected function beforeSave(): void
    {
        if (!$this->data['is_active'] || !$this->data['is_admin_panel_available']) {
            $otherAdminPanelExists = Domain::active()
                ->where('is_admin_panel_available', true)
                ->where('id', '!=', $this->record->id)
                ->exists();

            if (!$otherAdminPanelExists) {
                if (!$this->data['is_active'])
                    $this->addError('data.is_active', 'At least one active domain must have admin panel available.');

                if (!$this->data['is_admin_panel_available'])
                    $this->addError('data.is_admin_panel_available', 'At least one active domain must have admin panel available.');

                Notification::make()
                    ->danger()
                    ->title('Cannot disable admin panel')
                    ->body('At least one active domain must have admin panel available.')
                    ->send();

                $this->halt();
            }
        }
    }
}
