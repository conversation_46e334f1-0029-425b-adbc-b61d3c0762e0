<?php

namespace App\Policies;

use App\Models\User;
use Illuminate\Auth\Access\HandlesAuthorization;
use Illuminate\Database\Eloquent\Model;

abstract class BasePolicy
{
    use HandlesAuthorization;

    protected string $modelName;

    public function viewAny(User $user): bool
    {
        return $user->can('view '.$this->modelName);
    }

    public function view(User $user, Model $model): bool
    {
        return $user->can('view '.$this->modelName);
    }

    public function create(User $user): bool
    {
        return $user->can('create '.$this->modelName);
    }

    public function update(User $user, Model $model): bool
    {
        return $user->can('update '.$this->modelName);
    }

    public function delete(User $user, Model $model): bool
    {
        return $user->can('delete '.$this->modelName);
    }
}
