<?php

use App\Livewire\LinkPage;
use Illuminate\Support\Facades\Route;

Route::get('/ip', function () {
    dd(
        asset('a.css'),
        request()->ips(),
        request()->getClientIps(),
        request()->headers->all(),
        request()->server(),
    );
})->name('ip');

// Main shortened URL redirect route
Route::get('/{short_path}', LinkPage::class)
    ->where('short_path', '.*')
    ->name('link.redirect');
