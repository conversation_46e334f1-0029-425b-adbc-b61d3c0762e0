FROM composer:2 as composer

# Stage 1: Composer dependencies
FROM dunglas/frankenphp AS builder
WORKDIR /app

RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    git \
    unzip \
    libpq-dev \
    libonig-dev \
    libssl-dev \
    libxml2-dev \
    libcurl4-openssl-dev \
    libicu-dev \
    libzip-dev \
    && docker-php-ext-install \
    pcntl \
    pdo_pgsql \
    pgsql \
    opcache \
    intl \
    zip \
    && pecl install redis \
    && docker-php-ext-enable redis \
    && apt-get autoremove -y && apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Copy only composer files first for optimal caching
COPY composer.json composer.lock ./

COPY --link --chown=${WWWUSER}:${WWWUSER} --from=composer /usr/bin/composer /usr/bin/composer

# Install dependencies without dev packages
RUN composer install \
    --no-dev \
    --no-interaction \
    --no-scripts \
    --optimize-autoloader

# Stage 2: Node dependencies and frontend build
FROM node:lts AS node
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY resources/ ./resources/
COPY vite.config.js ./
RUN npm run build

# Stage 3: Final FrankenPHP image
FROM dunglas/frankenphp AS runner
WORKDIR /app

RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    git \
    unzip \
    libpq-dev \
    libonig-dev \
    libssl-dev \
    libxml2-dev \
    libcurl4-openssl-dev \
    libicu-dev \
    libzip-dev \
    && docker-php-ext-install \
    pcntl \
    pdo_pgsql \
    pgsql \
    opcache \
    intl \
    zip \
    && pecl install redis \
    && docker-php-ext-enable redis \
    && apt-get autoremove -y && apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Copy from previous stages with correct permissions
COPY --from=builder --chown=appuser:appuser /app/vendor ./vendor
COPY --from=node --chown=appuser:appuser /app/public/build ./public/build

# Copy application files
COPY . .

# Set up entrypoint
COPY entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/entrypoint.sh

ENTRYPOINT ["entrypoint.sh"]
