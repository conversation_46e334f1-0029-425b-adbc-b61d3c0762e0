<?php

namespace Filament\Components;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class FilamentButtonTest extends TestCase
{
    use RefreshDatabase;

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_renders_with_default_text()
    {
        $view = $this->blade(
            '<x-filament-button :click="$click" />',
            ['click' => 'doSomething']
        );

        $view->assertSee('Button', false);
        $view->assertSee('wire:click="doSomething"', false);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_renders_with_custom_text()
    {
        $view = $this->blade(
            '<x-filament-button :click="$click" :text="$text" />',
            ['click' => 'doSomething', 'text' => 'Custom Button Text']
        );

        $view->assertSee('Custom Button Text', false);
        $view->assertSee('wire:click="doSomething"', false);
    }
}
