<?php

namespace Filament\Resources;

use App\Filament\Resources\DomainResource;
use App\Models\Domain;
use App\Models\Link;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DomainResourceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an active domain with admin panel to satisfy validation
        Domain::factory()->create([
            'host' => 'default-test-domain.com',
            'is_active' => true,
            'is_admin_panel_available' => true,
        ]);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_model()
    {
        $this->assertEquals(Domain::class, DomainResource::getModel());
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_navigation_group()
    {
        $this->assertEquals('Link Management', DomainResource::getNavigationGroup());
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_navigation_icon()
    {
        $this->assertEquals('heroicon-o-rectangle-stack', DomainResource::getNavigationIcon());
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_slug()
    {
        $this->assertEquals('domains', DomainResource::getSlug());
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_record_title_attribute()
    {
        $this->assertEquals('host', DomainResource::getRecordTitleAttribute());
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_empty_globally_searchable_attributes()
    {
        $this->assertEquals(
            [],
            DomainResource::getGloballySearchableAttributes()
        );
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_pages()
    {
        $pages = DomainResource::getPages();

        $this->assertArrayHasKey('index', $pages);
        $this->assertArrayHasKey('create', $pages);
        $this->assertArrayHasKey('edit', $pages);
        $this->assertArrayHasKey('history', $pages);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_relations()
    {
        $relations = DomainResource::getRelations();

        $this->assertCount(1, $relations);
        $this->assertContains('App\Filament\Resources\DomainResource\RelationManagers\LinksRelationManager', $relations);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_delete_domain_without_links()
    {
        $domain = Domain::factory()->create([
            'host' => 'deletable-domain.com',
            'is_active' => true,
            'is_admin_panel_available' => false,
        ]);

        // Verify domain exists
        $this->assertDatabaseHas('domains', ['id' => $domain->id]);

        // Delete the domain
        $domain->delete();

        // Verify domain is deleted
        $this->assertDatabaseMissing('domains', ['id' => $domain->id]);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_prevents_deletion_of_domain_with_links()
    {
        $domain = Domain::factory()->create([
            'host' => 'domain-with-links.com',
            'is_active' => true,
            'is_admin_panel_available' => false,
        ]);

        $link = Link::factory()->create();
        $domain->links()->attach($link);

        // Verify domain has links
        $this->assertTrue($domain->links()->exists());

        // Verify domain exists
        $this->assertDatabaseHas('domains', ['id' => $domain->id]);

        // The database constraint should prevent deletion
        $this->expectException(\Illuminate\Database\QueryException::class);

        $domain->delete();
    }
}
