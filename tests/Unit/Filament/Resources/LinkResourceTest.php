<?php

namespace Filament\Resources;

use App\Filament\Resources\LinkResource;
use App\Models\Domain;
use App\Models\Link;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LinkResourceTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an active domain with admin panel to satisfy validation
        Domain::factory()->create([
            'host' => 'default-test-domain.com',
            'is_active' => true,
            'is_admin_panel_available' => true,
        ]);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_model()
    {
        $this->assertEquals(Link::class, LinkResource::getModel());
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_navigation_group()
    {
        $this->assertEquals('Link Management', LinkResource::getNavigationGroup());
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_navigation_icon()
    {
        $this->assertEquals('heroicon-o-link', LinkResource::getNavigationIcon());
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_slug()
    {
        $this->assertEquals('links', LinkResource::getSlug());
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_record_title_attribute()
    {
        $this->assertEquals('short_path', LinkResource::getRecordTitleAttribute());
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_globally_searchable_attributes()
    {
        $this->assertEquals(
            ['slug', 'original_url', 'description'],
            LinkResource::getGloballySearchableAttributes()
        );
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_pages()
    {
        $pages = LinkResource::getPages();

        $this->assertArrayHasKey('index', $pages);
        $this->assertArrayHasKey('create', $pages);
        $this->assertArrayHasKey('edit', $pages);
        $this->assertArrayHasKey('history', $pages);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_has_correct_widgets()
    {
        $widgets = LinkResource::getWidgets();

        $this->assertCount(4, $widgets);
        $this->assertContains('App\Filament\Resources\LinkResource\Widgets\LinkVisitsCountChart', $widgets);
        $this->assertContains('App\Filament\Resources\LinkResource\Widgets\LinkVisitsByBrowserPieChart', $widgets);
        $this->assertContains('App\Filament\Resources\LinkResource\Widgets\LinkVisitsByPlatformPieChart', $widgets);
        $this->assertContains('App\Filament\Resources\LinkResource\Widgets\LinkVisitsByCountryPieChart', $widgets);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_includes_visit_count_in_eloquent_query()
    {
        $query = LinkResource::getEloquentQuery();

        // Check if the query includes the withCount('visits') call
        $this->assertStringContainsString(
            'select "links".*, (select count(*) from "link_visits" where "links"."id" = "link_visits"."link_id") as "visits_count"',
            $query->toSql()
        );
    }
}
