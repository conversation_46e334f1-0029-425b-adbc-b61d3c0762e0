<?php

namespace Helpers;

use App\Models\Domain;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Tests\TestCase;

class HelpersTest extends TestCase
{
    use RefreshDatabase;

    protected Domain $domain;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a test domain
        $this->domain = Domain::factory()->create([
            'host' => 'example.com',
            'protocol' => 'https',
            'is_active' => true,
            'is_admin_panel_available' => true,
        ]);
    }

    /**
     * Tests for current_domain() helper
     */
    #[\PHPUnit\Framework\Attributes\Test]
    public function current_domain_returns_domain_matching_http_host()
    {
        // Mock the request with the domain's host
        $this->app->instance('request', Request::create('https://example.com'));

        // Call the helper function
        $result = current_domain();

        // Assert that the correct domain is returned
        $this->assertInstanceOf(Domain::class, $result);
        $this->assertEquals($this->domain->id, $result->id);
        $this->assertEquals('example.com', $result->host);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function current_domain_returns_domain_with_port_in_host()
    {
        // Create a test domain with port in host
        $domainWithPort = Domain::factory()->create([
            'host' => 'example.com:8080',
            'protocol' => 'https',
            'is_active' => true,
            'is_admin_panel_available' => true,
        ]);

        // Mock the request with the domain's host including port
        $this->app->instance('request', Request::create('https://example.com:8080'));

        // Call the helper function
        $result = current_domain();

        // Assert that the correct domain is returned
        $this->assertInstanceOf(Domain::class, $result);
        $this->assertEquals($domainWithPort->id, $result->id);
        $this->assertEquals('example.com:8080', $result->host);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function current_domain_returns_null_when_no_matching_domain_exists()
    {
        // Mock the request with a host that doesn't exist in the database
        $this->app->instance('request', Request::create('https://nonexistent-domain.com'));

        // Call the helper function
        $result = current_domain();

        // Assert that null is returned
        $this->assertNull($result);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function current_domain_returns_correct_domain_when_multiple_domains_exist()
    {
        // Create multiple domains
        Domain::factory()->create([
            'host' => 'example1.com',
            'protocol' => 'https',
            'is_active' => true,
            'is_admin_panel_available' => true,
        ]);

        $targetDomain = Domain::factory()->create([
            'host' => 'example2.com',
            'protocol' => 'https',
            'is_active' => true,
            'is_admin_panel_available' => true,
        ]);

        Domain::factory()->create([
            'host' => 'example3.com',
            'protocol' => 'https',
            'is_active' => true,
            'is_admin_panel_available' => true,
        ]);

        // Mock the request with the target domain's host
        $this->app->instance('request', Request::create('https://example2.com'));

        // Call the helper function
        $result = current_domain();

        // Assert that the correct domain is returned
        $this->assertInstanceOf(Domain::class, $result);
        $this->assertEquals($targetDomain->id, $result->id);
        $this->assertEquals('example2.com', $result->host);
    }
}
