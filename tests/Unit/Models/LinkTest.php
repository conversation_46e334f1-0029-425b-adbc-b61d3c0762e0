<?php

namespace Models;

use App\Models\Domain;
use App\Models\Link;
use App\Models\LinkVisit;
use App\Models\Tag;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class LinkTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_can_create_a_link()
    {
        // Create a link with a slug
        $link = new Link();
        $link->original_url = 'https://example.com';
        $link->password = 'secret';
        $link->available_at = now()->subDay();
        $link->unavailable_at = now()->addDay();
        $link->forward_query_parameters = true;
        $link->slug = 'example';
        $link->description = 'Example link';
        $link->visit_count = 0;
        $link->send_ref_query_parameter = true;
        $link->is_active = true;
        $link->save();

        $this->assertDatabaseHas('links', [
            'original_url' => 'https://example.com',
            'password' => 'secret',
            'forward_query_parameters' => true,
            'slug' => 'example',
            'description' => 'Example link',
            'visit_count' => 0,
            'send_ref_query_parameter' => true,
            'is_active' => true,
        ]);

        // The short_path should be set to the slug by the observer
        $this->assertEquals('example', $link->short_path);

        $this->assertEquals('https://example.com', $link->original_url);
        $this->assertEquals('secret', $link->password);
        $this->assertTrue($link->forward_query_parameters);
        $this->assertEquals('example', $link->slug);
        $this->assertEquals('Example link', $link->description);
        $this->assertEquals(0, $link->visit_count);
        $this->assertTrue($link->send_ref_query_parameter);
    }

    #[Test]
    public function it_has_domains_relationship()
    {
        $link = Link::factory()->create();
        $domain = Domain::factory()->create();

        $link->domains()->attach($domain);

        $this->assertTrue($link->domains->contains($domain));
        $this->assertEquals(1, $link->domains->count());
    }

    #[Test]
    public function it_has_tags_relationship()
    {
        $link = Link::factory()->create();
        $tag = Tag::factory()->create();

        $link->tags()->attach($tag);

        $this->assertTrue($link->tags->contains($tag));
        $this->assertEquals(1, $link->tags->count());
    }

    #[Test]
    public function it_has_visits_relationship()
    {
        $link = Link::factory()->create();
        $visit = LinkVisit::factory()->create(['link_id' => $link->id]);

        $this->assertTrue($link->visits->contains($visit));
        $this->assertEquals(1, $link->visits->count());
    }

    #[Test]
    public function it_can_be_converted_to_string()
    {
        $link = new Link();
        $link->original_url = 'https://example.com';
        $link->slug = 'testslug';
        $link->forward_query_parameters = true;
        $link->send_ref_query_parameter = true;
        $link->save();

        $this->assertEquals('testslug', (string)$link);
    }

    #[Test]
    public function it_has_available_scope()
    {
        // Create a link that is available (no dates, active)
        Link::factory()->create([
            'available_at' => null,
            'unavailable_at' => null,
            'is_active' => true,
        ]);

        // Create a link that is available (within dates, active)
        Link::factory()->create([
            'available_at' => now()->subDay(),
            'unavailable_at' => now()->addDay(),
            'is_active' => true,
        ]);

        // Create a link that is not available (future available date)
        Link::factory()->create([
            'available_at' => now()->addDay(),
            'unavailable_at' => null,
            'is_active' => true,
        ]);

        // Create a link that is not available (past unavailable date)
        Link::factory()->create([
            'available_at' => null,
            'unavailable_at' => now()->subDay(),
            'is_active' => true,
        ]);

        // Create a link that is not available (inactive)
        Link::factory()->create([
            'available_at' => null,
            'unavailable_at' => null,
            'is_active' => false,
        ]);

        $availableLinks = Link::available()->get();

        $this->assertEquals(2, $availableLinks->count());
    }

    #[Test]
    public function it_excludes_inactive_links_from_available_scope()
    {
        // Create an active link
        $activeLink = Link::factory()->create([
            'is_active' => true,
            'available_at' => null,
            'unavailable_at' => null,
        ]);

        // Create an inactive link
        $inactiveLink = Link::factory()->create([
            'is_active' => false,
            'available_at' => null,
            'unavailable_at' => null,
        ]);

        $availableLinks = Link::available()->get();

        $this->assertEquals(1, $availableLinks->count());
        $this->assertTrue($availableLinks->contains($activeLink));
        $this->assertFalse($availableLinks->contains($inactiveLink));
    }

    #[Test]
    public function it_has_has_password_scope()
    {
        Link::factory()->create(['password' => 'secret']);
        Link::factory()->create(['password' => null]);

        $linksWithPassword = Link::whereNotNull('password')->get();

        $this->assertEquals(1, $linksWithPassword->count());
        $this->assertNotNull($linksWithPassword->first()->password);
    }

    #[Test]
    public function it_has_for_current_domain_scope()
    {
        // This test is more complex as it requires mocking the current_domain helper
        // For simplicity, we'll just test that the method exists
        $this->assertTrue(method_exists(Link::class, 'scopeForCurrentDomain'));
    }

    #[Test]
    public function it_has_guarded_attributes()
    {
        $link = new Link();

        $this->assertEquals(['short_path'], $link->getGuarded());
    }

    #[Test]
    public function it_has_logs_activity_trait()
    {
        $link = new Link();

        $this->assertTrue(method_exists($link, 'getActivitylogOptions'));
    }

    #[Test]
    public function it_has_is_available_attribute()
    {
        // Available link (no dates, active)
        $link1 = Link::factory()->create([
            'available_at' => null,
            'unavailable_at' => null,
            'is_active' => true,
        ]);

        // Available link (within dates, active)
        $link2 = Link::factory()->create([
            'available_at' => now()->subDay(),
            'unavailable_at' => now()->addDay(),
            'is_active' => true,
        ]);

        // Not available link (future available date)
        $link3 = Link::factory()->create([
            'available_at' => now()->addDay(),
            'unavailable_at' => null,
            'is_active' => true,
        ]);

        // Not available link (inactive)
        $link4 = Link::factory()->create([
            'available_at' => null,
            'unavailable_at' => null,
            'is_active' => false,
        ]);

        $this->assertTrue($link1->isAvailable);
        $this->assertTrue($link2->isAvailable);
        $this->assertFalse($link3->isAvailable);
        $this->assertFalse($link4->isAvailable);
    }

    #[Test]
    public function it_has_has_password_attribute()
    {
        $link1 = Link::factory()->create(['password' => 'secret']);
        $link2 = Link::factory()->create(['password' => null]);

        $this->assertTrue($link1->hasPassword);
        $this->assertFalse($link2->hasPassword);
    }

    #[Test]
    public function it_casts_date_attributes()
    {
        $link = Link::factory()->create([
            'available_at' => '2023-01-01 00:00:00',
            'unavailable_at' => '2023-12-31 23:59:59',
        ]);

        $this->assertInstanceOf(Carbon::class, $link->available_at);
        $this->assertInstanceOf(Carbon::class, $link->unavailable_at);
        $this->assertEquals('2023-01-01 00:00:00', $link->available_at->format('Y-m-d H:i:s'));
        $this->assertEquals('2023-12-31 23:59:59', $link->unavailable_at->format('Y-m-d H:i:s'));
    }

    protected function setUp(): void
    {
        parent::setUp();

        // Create an active domain with admin panel to satisfy validation
        Domain::factory()->create([
            'host' => 'default-test-domain.com',
            'is_active' => true,
            'is_admin_panel_available' => true,
        ]);
    }
}
