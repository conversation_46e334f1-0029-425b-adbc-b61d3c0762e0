<?php

namespace Tests\Feature;

use App\Models\Domain;
use App\Models\Link;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LinkResourceFormTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create an active domain with admin panel to satisfy validation
        Domain::factory()->create([
            'host' => 'default-test-domain.com',
            'is_active' => true,
            'is_admin_panel_available' => true,
        ]);

        // Create a super admin user
        $this->user = User::factory()->create([
            'is_super_admin' => true,
        ]);

        $this->actingAs($this->user);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_includes_is_active_field_in_form()
    {
        // This test verifies that the is_active field is properly configured in the model
        // and can be used in forms. The actual form structure testing would require
        // more complex Livewire component testing.

        $link = Link::factory()->create(['is_active' => true]);

        // Verify the field exists and is castable
        $this->assertTrue($link->is_active);
        $this->assertIsBool($link->is_active);

        // Verify it's in the casts array
        $casts = $link->getCasts();
        $this->assertArrayHasKey('is_active', $casts);
        $this->assertEquals('boolean', $casts['is_active']);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_create_link_with_is_active_field()
    {
        $domain = Domain::factory()->create([
            'is_active' => true,
        ]);

        $linkData = [
            'original_url' => 'https://example.com',
            'is_active' => false,
            'forward_query_parameters' => false,
            'send_ref_query_parameter' => false,
        ];

        $link = Link::create($linkData);

        $this->assertDatabaseHas('links', [
            'original_url' => 'https://example.com',
            'is_active' => false,
        ]);

        $this->assertFalse($link->is_active);
        $this->assertFalse($link->isAvailable);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_update_link_is_active_status()
    {
        $link = Link::factory()->create([
            'is_active' => true,
        ]);

        $this->assertTrue($link->is_active);

        $link->update(['is_active' => false]);

        $this->assertFalse($link->fresh()->is_active);
        $this->assertFalse($link->fresh()->isAvailable);
    }
}
